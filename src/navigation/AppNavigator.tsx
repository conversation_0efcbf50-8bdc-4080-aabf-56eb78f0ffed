import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import {
  NavigationContainer,
  DefaultTheme,
  DarkTheme,
} from '@react-navigation/native';

import { RootStackParamList, MainTabParamList } from '../types';
import ObjectivesScreen from '../screens/objectives/ObjectivesScreen';
import { useState } from 'react';
import { useAppStore } from '../stores/useAppStore';
import LoadingScreen from '../screens/common/LoadingScreen';
import { StatusBar } from 'react-native';
import AuthNavigator from './AuthNavigator';

const RootStack = createNativeStackNavigator<RootStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator: React.FC = () => {
    return (
        <>
            <MainTab.Navigator
                screenOptions={
                    {
                        headerShown: false,
                        tabBarShowLabel: false,
                        tabBarStyle: {
                            backgroundColor: '#fff',
                            borderTopWidth: 0,
                            elevation: 0,
                        },
                    }
                }
                >
                <MainTab.Screen
                    name="Objectives"
                    component={ObjectivesScreen}
                    options={{
                        title: "Objectives"
                    }}
                />
            </MainTab.Navigator>
        </>
    )
}

const AppNavigator: React.FC = () => {
    const [isInitializing, setIsInitializing] = useState(true);
    const [initializationError, setInitializationError] = useState<string | null>(
        null,
    );

    const { auth, user, ui } = useAppStore();

    useEffect(() => {
        const initializeServices = async () => {
           //TOOD: Initialize centrifugo
           setIsInitializing(false);
        };

        initializeServices();

    }, []);

    if (isInitializing) {
        return <LoadingScreen message="Initializing app services..." />;
    }

    return (
        <NavigationContainer theme={DarkTheme}>
            <StatusBar 
                barStyle={"dark-content"}
                backgroundColor={DarkTheme.colors.card}
            />
            <RootStack.Navigator
                screenOptions={{
                    headerShown: false,
                    animation: 'slide_from_right',
                }}
            >
                {!auth.isAuthenticated ? (
                    <RootStack.Screen
                        name="Auth"
                        component={AuthNavigator}
                        options={{
                            animationTypeForReplace: "push",
                        }}
                    />
                ) : (
                <RootStack.Screen
                    name="Main"
                    component={MainTabNavigator}
                    options={{
                        animationTypeForReplace: "push",
                    }}
                    />
                )}
            </RootStack.Navigator>
        </NavigationContainer>
    )


}

export default AppNavigator;