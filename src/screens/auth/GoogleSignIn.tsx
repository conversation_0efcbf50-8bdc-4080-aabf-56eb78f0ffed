import { Button } from 'react-native';
import { GoogleAuthProvider, getAuth, signInWithCredential } from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

async function onGoogleButtonPress() {
  await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
  const signInResult = await GoogleSignin.signIn();

  let idToken = signInResult.data?.idToken;

  if (!idToken) {
    throw new Error('No ID token found');
  }

  const googleCredential = GoogleAuthProvider.credential(signInResult.data?.idToken);

  return signInWithCredential(getAuth(), googleCredential);
}

const GoogleSignIn: React.FC = () => {
  return (
    <Button
      title="Google Sign-In"
      onPress={() => onGoogleButtonPress().then((test) => 
        console.log('Signed in with Google!', test)
      )}
    />
  );
}

export default GoogleSignIn;
